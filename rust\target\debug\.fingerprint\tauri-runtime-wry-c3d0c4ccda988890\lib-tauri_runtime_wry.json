{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 17231900515118247201, "deps": [[376837177317575824, "softbuffer", false, 2596192284037492366], [442785307232013896, "tauri_runtime", false, 8587187459367707119], [3150220818285335163, "url", false, 15730960352794477868], [3722963349756955755, "once_cell", false, 13898173386827918274], [4143744114649553716, "raw_window_handle", false, 6837381887394946460], [5986029879202738730, "log", false, 15132848699422293037], [7752760652095876438, "build_script_build", false, 2147083151540039373], [8539587424388551196, "webview2_com", false, 2428775030815779815], [9010263965687315507, "http", false, 3756583827281232345], [11050281405049894993, "tauri_utils", false, 1345559957486509526], [13116089016666501665, "windows", false, 12421774094317942604], [13223659721939363523, "tao", false, 2689460651789268129], [14794439852947137341, "wry", false, 3046019348089754522]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-c3d0c4ccda988890\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}