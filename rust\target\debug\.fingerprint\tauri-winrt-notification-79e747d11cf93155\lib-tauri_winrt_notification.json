{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 5347358027863023418, "path": 5451450293158687294, "deps": [[1462335029370885857, "quick_xml", false, 17578650478498907910], [3334271191048661305, "windows_version", false, 16842729625060924107], [10806645703491011684, "thiserror", false, 15657458092141047087], [13116089016666501665, "windows", false, 12421774094317942604]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-79e747d11cf93155\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}