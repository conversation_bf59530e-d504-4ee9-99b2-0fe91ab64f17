{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 8276155916380437441, "path": 1288308961366000512, "deps": [[442785307232013896, "build_script_build", false, 9991982447876990016], [3150220818285335163, "url", false, 10260461479629395240], [4143744114649553716, "raw_window_handle", false, 8264662274193606652], [7606335748176206944, "dpi", false, 17159485969296418934], [9010263965687315507, "http", false, 14438606696231830319], [9689903380558560274, "serde", false, 7175605069985593048], [10806645703491011684, "thiserror", false, 14203071536756777833], [11050281405049894993, "tauri_utils", false, 2262565235679204269], [13116089016666501665, "windows", false, 15799024179589216305], [15367738274754116744, "serde_json", false, 1417905963242708491], [16727543399706004146, "cookie", false, 12649517156996630295]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-ffece6a304339303\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}