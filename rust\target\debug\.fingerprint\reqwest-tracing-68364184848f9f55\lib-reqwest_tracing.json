{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"opentelemetry_0_13\", \"opentelemetry_0_13_pkg\", \"opentelemetry_0_14\", \"opentelemetry_0_14_pkg\", \"opentelemetry_0_15\", \"opentelemetry_0_15_pkg\", \"opentelemetry_0_16\", \"opentelemetry_0_16_pkg\", \"opentelemetry_0_17\", \"opentelemetry_0_17_pkg\", \"opentelemetry_0_18\", \"opentelemetry_0_18_pkg\", \"opentelemetry_0_19\", \"opentelemetry_0_19_pkg\", \"opentelemetry_0_20\", \"opentelemetry_0_20_pkg\", \"opentelemetry_0_21\", \"opentelemetry_0_21_pkg\", \"opentelemetry_0_22\", \"opentelemetry_0_22_pkg\", \"tracing-opentelemetry_0_12_pkg\", \"tracing-opentelemetry_0_13_pkg\", \"tracing-opentelemetry_0_14_pkg\", \"tracing-opentelemetry_0_16_pkg\", \"tracing-opentelemetry_0_17_pkg\", \"tracing-opentelemetry_0_18_pkg\", \"tracing-opentelemetry_0_19_pkg\", \"tracing-opentelemetry_0_20_pkg\", \"tracing-opentelemetry_0_22_pkg\", \"tracing-opentelemetry_0_23_pkg\"]", "target": 13463648648797123179, "profile": 5347358027863023418, "path": 6067412448717092366, "deps": [[4365722290128161641, "task_local_extensions", false, 2291195490384359659], [7244058819997729774, "reqwest", false, 13513915520156233617], [8606274917505247608, "tracing", false, 1145649429643657630], [9678799920983747518, "matchit", false, 5919554849643943670], [11946729385090170470, "async_trait", false, 6969772722108267470], [13625485746686963219, "anyhow", false, 15003930525526841947], [15901872898063384487, "reqwest_middleware", false, 18241977156357910137]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-tracing-68364184848f9f55\\dep-lib-reqwest_tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}