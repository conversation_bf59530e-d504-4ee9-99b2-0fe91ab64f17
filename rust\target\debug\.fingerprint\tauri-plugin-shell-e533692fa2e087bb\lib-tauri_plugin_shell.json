{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 5347358027863023418, "path": 2911265107896841603, "deps": [[500211409582349667, "shared_child", false, 8512790132503747870], [1582828171158827377, "build_script_build", false, 4277959345621372128], [5986029879202738730, "log", false, 15132848699422293037], [9451456094439810778, "regex", false, 15679568338494819172], [9538054652646069845, "tokio", false, 1845311123818853277], [9689903380558560274, "serde", false, 18066533925557853048], [10755362358622467486, "tauri", false, 10248244220009135351], [10806645703491011684, "thiserror", false, 15657458092141047087], [11337703028400419576, "os_pipe", false, 7048640408531537499], [14564311161534545801, "encoding_rs", false, 6984454549018752457], [15367738274754116744, "serde_json", false, 11959657437500097803], [16192041687293812804, "open", false, 1508807782546719492]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-e533692fa2e087bb\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}