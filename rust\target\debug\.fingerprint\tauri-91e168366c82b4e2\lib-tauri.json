{"rustc": 12488743700189009532, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"http-range\", \"macos-private-api\", \"protocol-asset\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 8276155916380437441, "path": 2484673656501667652, "deps": [[40386456601120721, "percent_encoding", false, 11201700476752682152], [442785307232013896, "tauri_runtime", false, 14500700714929902453], [1200537532907108615, "url<PERSON><PERSON>n", false, 13022436379605212024], [3150220818285335163, "url", false, 10260461479629395240], [4143744114649553716, "raw_window_handle", false, 8264662274193606652], [4341921533227644514, "muda", false, 15336056966443270371], [4919829919303820331, "serialize_to_javascript", false, 110994999894808907], [5986029879202738730, "log", false, 7320443587293871305], [7752760652095876438, "tauri_runtime_wry", false, 517716286769433945], [8351317599104215083, "tray_icon", false, 7965202255727595668], [8539587424388551196, "webview2_com", false, 2260269201761289649], [8866577183823226611, "http_range", false, 14986734857648202560], [9010263965687315507, "http", false, 14438606696231830319], [9228235415475680086, "tauri_macros", false, 4030505734381534696], [9538054652646069845, "tokio", false, 8619070681005270242], [9689903380558560274, "serde", false, 7175605069985593048], [9920160576179037441, "getrandom", false, 2146881975018653523], [10229185211513642314, "mime", false, 799632151728794554], [10629569228670356391, "futures_util", false, 1180256261339817079], [10755362358622467486, "build_script_build", false, 9546431537315807055], [10806645703491011684, "thiserror", false, 14203071536756777833], [11050281405049894993, "tauri_utils", false, 2262565235679204269], [11989259058781683633, "dunce", false, 10289024619304111381], [12565293087094287914, "window_vibrancy", false, 2086736311755211253], [12986574360607194341, "serde_repr", false, 8271347498386200132], [13077543566650298139, "heck", false, 8273720639801179232], [13116089016666501665, "windows", false, 15799024179589216305], [13625485746686963219, "anyhow", false, 7988853329195065960], [15367738274754116744, "serde_json", false, 1417905963242708491], [16928111194414003569, "dirs", false, 5182739481406056855], [17155886227862585100, "glob", false, 12561197479529836342]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-91e168366c82b4e2\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}