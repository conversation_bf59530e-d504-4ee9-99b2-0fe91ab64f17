{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 5347358027863023418, "path": 1288308961366000512, "deps": [[442785307232013896, "build_script_build", false, 9991982447876990016], [3150220818285335163, "url", false, 15730960352794477868], [4143744114649553716, "raw_window_handle", false, 6837381887394946460], [7606335748176206944, "dpi", false, 14536395996543022260], [9010263965687315507, "http", false, 3756583827281232345], [9689903380558560274, "serde", false, 18066533925557853048], [10806645703491011684, "thiserror", false, 15657458092141047087], [11050281405049894993, "tauri_utils", false, 1345559957486509526], [13116089016666501665, "windows", false, 12421774094317942604], [15367738274754116744, "serde_json", false, 11959657437500097803], [16727543399706004146, "cookie", false, 5837253797361730432]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-5fed210606ef349c\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}