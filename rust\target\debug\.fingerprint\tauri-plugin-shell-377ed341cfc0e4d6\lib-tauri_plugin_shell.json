{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 8276155916380437441, "path": 2911265107896841603, "deps": [[500211409582349667, "shared_child", false, 1995390142623171291], [1582828171158827377, "build_script_build", false, 14668054621289471235], [5986029879202738730, "log", false, 7320443587293871305], [9451456094439810778, "regex", false, 12250754408636079168], [9538054652646069845, "tokio", false, 8619070681005270242], [9689903380558560274, "serde", false, 7175605069985593048], [10755362358622467486, "tauri", false, 8873184714410150099], [10806645703491011684, "thiserror", false, 14203071536756777833], [11337703028400419576, "os_pipe", false, 4030666992685201908], [14564311161534545801, "encoding_rs", false, 9995490080141555987], [15367738274754116744, "serde_json", false, 1417905963242708491], [16192041687293812804, "open", false, 16457997080203196660]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-377ed341cfc0e4d6\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}