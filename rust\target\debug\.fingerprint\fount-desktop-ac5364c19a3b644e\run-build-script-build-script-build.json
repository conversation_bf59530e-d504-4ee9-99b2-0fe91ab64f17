{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17932638827197247375, "build_script_build", false, 14821918417808399383], [10755362358622467486, "build_script_build", false, 4611701324750393771], [3834743577069889284, "build_script_build", false, 7526714231947764627], [13890802266741835355, "build_script_build", false, 12160857531488773053], [12783828711503588811, "build_script_build", false, 4165053365432618229], [1582828171158827377, "build_script_build", false, 4277959345621372128], [11721252211900136025, "build_script_build", false, 15081921822872552394], [16445525635439251275, "build_script_build", false, 3863778831957812798]], "local": [{"RerunIfChanged": {"output": "debug\\build\\fount-desktop-ac5364c19a3b644e\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}