D:\fount\rust\target\debug\libfount_types.rlib: D:\fount\rust\crates\fount-types\src\ai_source.rs D:\fount\rust\crates\fount-types\src\ai_source_generator.rs D:\fount\rust\crates\fount-types\src\base_defs.rs D:\fount\rust\crates\fount-types\src\char_api.rs D:\fount\rust\crates\fount-types\src\import_handler_api.rs D:\fount\rust\crates\fount-types\src\lib.rs D:\fount\rust\crates\fount-types\src\plugin_api.rs D:\fount\rust\crates\fount-types\src\prompt_struct.rs D:\fount\rust\crates\fount-types\src\shell_api.rs D:\fount\rust\crates\fount-types\src\user_api.rs D:\fount\rust\crates\fount-types\src\world_api.rs
