["\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-7804e153a23069aa\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-7804e153a23069aa\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-7804e153a23069aa\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-7804e153a23069aa\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-7804e153a23069aa\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-7804e153a23069aa\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-7804e153a23069aa\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-7804e153a23069aa\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\fount\\rust\\target\\debug\\build\\tauri-7804e153a23069aa\\out\\permissions\\path\\autogenerated\\default.toml"]