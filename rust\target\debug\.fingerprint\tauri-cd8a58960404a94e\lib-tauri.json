{"rustc": 12488743700189009532, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 5347358027863023418, "path": 2484673656501667652, "deps": [[40386456601120721, "percent_encoding", false, 9012493762664266909], [442785307232013896, "tauri_runtime", false, 8587187459367707119], [1200537532907108615, "url<PERSON><PERSON>n", false, 1158565901763283583], [3150220818285335163, "url", false, 15730960352794477868], [4143744114649553716, "raw_window_handle", false, 6837381887394946460], [4341921533227644514, "muda", false, 8576859610191499778], [4919829919303820331, "serialize_to_javascript", false, 14964844673505236039], [5986029879202738730, "log", false, 15132848699422293037], [7752760652095876438, "tauri_runtime_wry", false, 12449556384063566306], [8351317599104215083, "tray_icon", false, 13865081805837504825], [8539587424388551196, "webview2_com", false, 2428775030815779815], [9010263965687315507, "http", false, 3756583827281232345], [9228235415475680086, "tauri_macros", false, 145885604865598349], [9538054652646069845, "tokio", false, 1845311123818853277], [9689903380558560274, "serde", false, 18066533925557853048], [9920160576179037441, "getrandom", false, 2136205516894907621], [10229185211513642314, "mime", false, 1930215642508730926], [10629569228670356391, "futures_util", false, 17733622655136772691], [10755362358622467486, "build_script_build", false, 4611701324750393771], [10806645703491011684, "thiserror", false, 15657458092141047087], [11050281405049894993, "tauri_utils", false, 1345559957486509526], [11989259058781683633, "dunce", false, 17760563620292761020], [12565293087094287914, "window_vibrancy", false, 17626335207820208623], [12986574360607194341, "serde_repr", false, 8271347498386200132], [13077543566650298139, "heck", false, 739421779050117165], [13116089016666501665, "windows", false, 12421774094317942604], [13625485746686963219, "anyhow", false, 15003930525526841947], [15367738274754116744, "serde_json", false, 11959657437500097803], [16928111194414003569, "dirs", false, 17053880923834703077], [17155886227862585100, "glob", false, 12265146915998126324]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-cd8a58960404a94e\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}