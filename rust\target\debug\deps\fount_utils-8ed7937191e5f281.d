D:\fount\rust\target\debug\deps\libfount_utils-8ed7937191e5f281.rmeta: crates\fount-utils\src\lib.rs crates\fount-utils\src\console.rs crates\fount-utils\src\json_loader.rs crates\fount-utils\src\i18n.rs crates\fount-utils\src\exec.rs crates\fount-utils\src\proxy.rs crates\fount-utils\src\ratelimit.rs crates\fount-utils\src\ms.rs crates\fount-utils\src\escape.rs crates\fount-utils\src\await_timeout.rs crates\fount-utils\src\notify.rs crates\fount-utils\src\tray.rs crates\fount-utils\src\discord_rpc.rs crates\fount-utils\src\env.rs crates\fount-utils\src\locale.rs crates\fount-utils\src\verify_code.rs crates\fount-utils\src\sentry_tunnel.rs

D:\fount\rust\target\debug\deps\libfount_utils-8ed7937191e5f281.rlib: crates\fount-utils\src\lib.rs crates\fount-utils\src\console.rs crates\fount-utils\src\json_loader.rs crates\fount-utils\src\i18n.rs crates\fount-utils\src\exec.rs crates\fount-utils\src\proxy.rs crates\fount-utils\src\ratelimit.rs crates\fount-utils\src\ms.rs crates\fount-utils\src\escape.rs crates\fount-utils\src\await_timeout.rs crates\fount-utils\src\notify.rs crates\fount-utils\src\tray.rs crates\fount-utils\src\discord_rpc.rs crates\fount-utils\src\env.rs crates\fount-utils\src\locale.rs crates\fount-utils\src\verify_code.rs crates\fount-utils\src\sentry_tunnel.rs

D:\fount\rust\target\debug\deps\fount_utils-8ed7937191e5f281.d: crates\fount-utils\src\lib.rs crates\fount-utils\src\console.rs crates\fount-utils\src\json_loader.rs crates\fount-utils\src\i18n.rs crates\fount-utils\src\exec.rs crates\fount-utils\src\proxy.rs crates\fount-utils\src\ratelimit.rs crates\fount-utils\src\ms.rs crates\fount-utils\src\escape.rs crates\fount-utils\src\await_timeout.rs crates\fount-utils\src\notify.rs crates\fount-utils\src\tray.rs crates\fount-utils\src\discord_rpc.rs crates\fount-utils\src\env.rs crates\fount-utils\src\locale.rs crates\fount-utils\src\verify_code.rs crates\fount-utils\src\sentry_tunnel.rs

crates\fount-utils\src\lib.rs:
crates\fount-utils\src\console.rs:
crates\fount-utils\src\json_loader.rs:
crates\fount-utils\src\i18n.rs:
crates\fount-utils\src\exec.rs:
crates\fount-utils\src\proxy.rs:
crates\fount-utils\src\ratelimit.rs:
crates\fount-utils\src\ms.rs:
crates\fount-utils\src\escape.rs:
crates\fount-utils\src\await_timeout.rs:
crates\fount-utils\src\notify.rs:
crates\fount-utils\src\tray.rs:
crates\fount-utils\src\discord_rpc.rs:
crates\fount-utils\src\env.rs:
crates\fount-utils\src\locale.rs:
crates\fount-utils\src\verify_code.rs:
crates\fount-utils\src\sentry_tunnel.rs:

# env-dep:CARGO_PKG_NAME=fount-utils
# env-dep:CARGO_PKG_VERSION=0.1.0
