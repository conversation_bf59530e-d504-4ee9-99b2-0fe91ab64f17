D:\fount\rust\target\debug\deps\libfount_desktop-9b30d5232cc235cc.rmeta: crates\fount-desktop\src\lib.rs crates\fount-desktop\src\app.rs crates\fount-desktop\src\commands.rs crates\fount-desktop\src\menu.rs crates\fount-desktop\src\system_tray.rs crates\fount-desktop\src\window.rs crates\fount-desktop\src\protocol.rs

D:\fount\rust\target\debug\deps\libfount_desktop-9b30d5232cc235cc.rlib: crates\fount-desktop\src\lib.rs crates\fount-desktop\src\app.rs crates\fount-desktop\src\commands.rs crates\fount-desktop\src\menu.rs crates\fount-desktop\src\system_tray.rs crates\fount-desktop\src\window.rs crates\fount-desktop\src\protocol.rs

D:\fount\rust\target\debug\deps\fount_desktop-9b30d5232cc235cc.d: crates\fount-desktop\src\lib.rs crates\fount-desktop\src\app.rs crates\fount-desktop\src\commands.rs crates\fount-desktop\src\menu.rs crates\fount-desktop\src\system_tray.rs crates\fount-desktop\src\window.rs crates\fount-desktop\src\protocol.rs

crates\fount-desktop\src\lib.rs:
crates\fount-desktop\src\app.rs:
crates\fount-desktop\src\commands.rs:
crates\fount-desktop\src\menu.rs:
crates\fount-desktop\src\system_tray.rs:
crates\fount-desktop\src\window.rs:
crates\fount-desktop\src\protocol.rs:
