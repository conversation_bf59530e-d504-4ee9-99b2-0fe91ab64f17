cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\fount\rust\target\debug\build\tauri-plugin-window-state-f04c177f47ef227f\out\tauri-plugin-window-state-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-window-state-2.2.2\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
