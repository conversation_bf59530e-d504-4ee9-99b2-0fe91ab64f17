{"$message_type":"diagnostic","message":"unused import: `load_json_file_if_exists`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\config.rs","byte_start":273,"byte_end":297,"line_start":8,"line_end":8,"column_start":64,"column_end":88,"is_primary":true,"text":[{"text":"use fount_utils::json_loader::{load_json_file, save_json_file, load_json_file_if_exists};","highlight_start":64,"highlight_end":88}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\config.rs","byte_start":271,"byte_end":297,"line_start":8,"line_end":8,"column_start":62,"column_end":88,"is_primary":true,"text":[{"text":"use fount_utils::json_loader::{load_json_file, save_json_file, load_json_file_if_exists};","highlight_start":62,"highlight_end":88}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `load_json_file_if_exists`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\config.rs:8:64\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse fount_utils::json_loader::{load_json_file, save_json_file, load_json_file_if_exists};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `get_data_dir`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\config.rs","byte_start":360,"byte_end":372,"line_start":9,"line_end":9,"column_start":61,"column_end":73,"is_primary":true,"text":[{"text":"use crate::base::{get_config_path, get_default_config_path, get_data_dir, ensure_data_dir};","highlight_start":61,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\config.rs","byte_start":358,"byte_end":372,"line_start":9,"line_end":9,"column_start":59,"column_end":73,"is_primary":true,"text":[{"text":"use crate::base::{get_config_path, get_default_config_path, get_data_dir, ensure_data_dir};","highlight_start":59,"highlight_end":73}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `get_data_dir`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\config.rs:9:61\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::base::{get_config_path, get_default_config_path, get_data_dir, ensure_data_dir};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `State` and `post`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":107,"byte_end":111,"line_start":6,"line_end":6,"column_start":20,"column_end":24,"is_primary":true,"text":[{"text":"    routing::{get, post},","highlight_start":20,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":128,"byte_end":133,"line_start":7,"line_end":7,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"    extract::{State, Request},","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":105,"byte_end":111,"line_start":6,"line_end":6,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"    routing::{get, post},","highlight_start":18,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":101,"byte_end":102,"line_start":6,"line_end":6,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"    routing::{get, post},","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":111,"byte_end":112,"line_start":6,"line_end":6,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"    routing::{get, post},","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":128,"byte_end":135,"line_start":7,"line_end":7,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"    extract::{State, Request},","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":127,"byte_end":128,"line_start":7,"line_end":7,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"    extract::{State, Request},","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":142,"byte_end":143,"line_start":7,"line_end":7,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"    extract::{State, Request},","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `State` and `post`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\server.rs:6:20\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    routing::{get, post},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    extract::{State, Request},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `get_config_clone` and `set_start_time`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":610,"byte_end":626,"line_start":27,"line_end":27,"column_start":41,"column_end":57,"is_primary":true,"text":[{"text":"    config::{ServerConfig, load_config, get_config_clone},","highlight_start":41,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":671,"byte_end":685,"line_start":28,"line_end":28,"column_start":43,"column_end":57,"is_primary":true,"text":[{"text":"    base::{init_base, get_uptime_seconds, set_start_time},","highlight_start":43,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":608,"byte_end":626,"line_start":27,"line_end":27,"column_start":39,"column_end":57,"is_primary":true,"text":[{"text":"    config::{ServerConfig, load_config, get_config_clone},","highlight_start":39,"highlight_end":57}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\server.rs","byte_start":669,"byte_end":685,"line_start":28,"line_end":28,"column_start":41,"column_end":57,"is_primary":true,"text":[{"text":"    base::{init_base, get_uptime_seconds, set_start_time},","highlight_start":41,"highlight_end":57}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `get_config_clone` and `set_start_time`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\server.rs:27:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config::{ServerConfig, load_config, get_config_clone},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    base::{init_base, get_uptime_seconds, set_start_time},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Json`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":199,"byte_end":203,"line_start":9,"line_end":9,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    Json,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":193,"byte_end":203,"line_start":8,"line_end":9,"column_start":23,"column_end":9,"is_primary":true,"text":[{"text":"    response::Response,","highlight_start":23,"highlight_end":24},{"text":"    Json,","highlight_start":1,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Json`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\auth.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Json,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Algorithm`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":289,"byte_end":298,"line_start":11,"line_end":11,"column_start":82,"column_end":91,"is_primary":true,"text":[{"text":"use jsonwebtoken::{decode, encode, DecodingKey, EncodingKey, Header, Validation, Algorithm};","highlight_start":82,"highlight_end":91}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":287,"byte_end":298,"line_start":11,"line_end":11,"column_start":80,"column_end":91,"is_primary":true,"text":[{"text":"use jsonwebtoken::{decode, encode, DecodingKey, EncodingKey, Header, Validation, Algorithm};","highlight_start":80,"highlight_end":91}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Algorithm`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\auth.rs:11:82\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse jsonwebtoken::{decode, encode, DecodingKey, EncodingKey, Header, Validation, Algorithm};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `debug` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":613,"byte_end":617,"line_start":20,"line_end":20,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":21,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":626,"byte_end":631,"line_start":20,"line_end":20,"column_start":34,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":34,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":611,"byte_end":617,"line_start":20,"line_end":20,"column_start":19,"column_end":25,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":19,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":624,"byte_end":631,"line_start":20,"line_end":20,"column_start":32,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":32,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `debug` and `warn`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\auth.rs:20:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, warn, error, debug};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ServerConfig` and `save_config`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":673,"byte_end":684,"line_start":23,"line_end":23,"column_start":26,"column_end":37,"is_primary":true,"text":[{"text":"    config::{get_config, save_config, ServerConfig},","highlight_start":26,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":686,"byte_end":698,"line_start":23,"line_end":23,"column_start":39,"column_end":51,"is_primary":true,"text":[{"text":"    config::{get_config, save_config, ServerConfig},","highlight_start":39,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":671,"byte_end":698,"line_start":23,"line_end":23,"column_start":24,"column_end":51,"is_primary":true,"text":[{"text":"    config::{get_config, save_config, ServerConfig},","highlight_start":24,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":660,"byte_end":661,"line_start":23,"line_end":23,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"    config::{get_config, save_config, ServerConfig},","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":698,"byte_end":699,"line_start":23,"line_end":23,"column_start":51,"column_end":52,"is_primary":true,"text":[{"text":"    config::{get_config, save_config, ServerConfig},","highlight_start":51,"highlight_end":52}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `ServerConfig` and `save_config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\auth.rs:23:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config::{get_config, save_config, ServerConfig},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `console`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":793,"byte_end":800,"line_start":26,"line_end":26,"column_start":66,"column_end":73,"is_primary":true,"text":[{"text":"use fount_utils::{json_loader::{load_json_file, save_json_file}, console};","highlight_start":66,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":791,"byte_end":800,"line_start":26,"line_end":26,"column_start":64,"column_end":73,"is_primary":true,"text":[{"text":"use fount_utils::{json_loader::{load_json_file, save_json_file}, console};","highlight_start":64,"highlight_end":73}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":745,"byte_end":746,"line_start":26,"line_end":26,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"use fount_utils::{json_loader::{load_json_file, save_json_file}, console};","highlight_start":18,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":800,"byte_end":801,"line_start":26,"line_end":26,"column_start":73,"column_end":74,"is_primary":true,"text":[{"text":"use fount_utils::{json_loader::{load_json_file, save_json_file}, console};","highlight_start":73,"highlight_end":74}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `console`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\auth.rs:26:66\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse fount_utils::{json_loader::{load_json_file, save_json_file}, console};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `PermissionLevel` and `Status`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":856,"byte_end":871,"line_start":27,"line_end":27,"column_start":54,"column_end":69,"is_primary":true,"text":[{"text":"use fount_types::{User, LoginRequest, LoginResponse, PermissionLevel, Status};","highlight_start":54,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":873,"byte_end":879,"line_start":27,"line_end":27,"column_start":71,"column_end":77,"is_primary":true,"text":[{"text":"use fount_types::{User, LoginRequest, LoginResponse, PermissionLevel, Status};","highlight_start":71,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":854,"byte_end":879,"line_start":27,"line_end":27,"column_start":52,"column_end":77,"is_primary":true,"text":[{"text":"use fount_types::{User, LoginRequest, LoginResponse, PermissionLevel, Status};","highlight_start":52,"highlight_end":77}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `PermissionLevel` and `Status`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\auth.rs:27:54\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse fount_types::{User, LoginRequest, LoginResponse, PermissionLevel, Status};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Path`, `Query`, `Request`, `Response`, and `middleware`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":134,"byte_end":138,"line_start":7,"line_end":7,"column_start":15,"column_end":19,"is_primary":true,"text":[{"text":"    extract::{Path, Query, State, Request},","highlight_start":15,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":140,"byte_end":145,"line_start":7,"line_end":7,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"    extract::{Path, Query, State, Request},","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":154,"byte_end":161,"line_start":7,"line_end":7,"column_start":35,"column_end":42,"is_primary":true,"text":[{"text":"    extract::{Path, Query, State, Request},","highlight_start":35,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":246,"byte_end":254,"line_start":10,"line_end":10,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    response::{Json as ResponseJson, Response},","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":261,"byte_end":271,"line_start":11,"line_end":11,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    middleware,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":134,"byte_end":147,"line_start":7,"line_end":7,"column_start":15,"column_end":28,"is_primary":true,"text":[{"text":"    extract::{Path, Query, State, Request},","highlight_start":15,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":152,"byte_end":161,"line_start":7,"line_end":7,"column_start":33,"column_end":42,"is_primary":true,"text":[{"text":"    extract::{Path, Query, State, Request},","highlight_start":33,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":133,"byte_end":134,"line_start":7,"line_end":7,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"    extract::{Path, Query, State, Request},","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":161,"byte_end":162,"line_start":7,"line_end":7,"column_start":42,"column_end":43,"is_primary":true,"text":[{"text":"    extract::{Path, Query, State, Request},","highlight_start":42,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":244,"byte_end":254,"line_start":10,"line_end":10,"column_start":36,"column_end":46,"is_primary":true,"text":[{"text":"    response::{Json as ResponseJson, Response},","highlight_start":36,"highlight_end":46}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":223,"byte_end":224,"line_start":10,"line_end":10,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    response::{Json as ResponseJson, Response},","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":254,"byte_end":255,"line_start":10,"line_end":10,"column_start":46,"column_end":47,"is_primary":true,"text":[{"text":"    response::{Json as ResponseJson, Response},","highlight_start":46,"highlight_end":47}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":255,"byte_end":271,"line_start":10,"line_end":11,"column_start":47,"column_end":15,"is_primary":true,"text":[{"text":"    response::{Json as ResponseJson, Response},","highlight_start":47,"highlight_end":48},{"text":"    middleware,","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Path`, `Query`, `Request`, `Response`, and `middleware`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\endpoints.rs:7:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    extract::{Path, Query, State, Request},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    response::{Json as ResponseJson, Response},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    middleware,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Deserialize` and `Serialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":288,"byte_end":299,"line_start":13,"line_end":13,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":301,"byte_end":310,"line_start":13,"line_end":13,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":276,"byte_end":313,"line_start":13,"line_end":14,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":37},{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Deserialize` and `Serialize`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\endpoints.rs:13:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::sync::Arc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":317,"byte_end":331,"line_start":14,"line_end":14,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":313,"byte_end":333,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":20},{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::sync::Arc`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\endpoints.rs:14:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":337,"byte_end":347,"line_start":15,"line_end":15,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":333,"byte_end":349,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"use anyhow::Result;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `uuid::Uuid`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\endpoints.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse uuid::Uuid;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `debug`, `error`, `info`, and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":383,"byte_end":387,"line_start":17,"line_end":17,"column_start":15,"column_end":19,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":15,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":389,"byte_end":393,"line_start":17,"line_end":17,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":21,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":395,"byte_end":400,"line_start":17,"line_end":17,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":402,"byte_end":407,"line_start":17,"line_end":17,"column_start":34,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":34,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":369,"byte_end":410,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":1,"highlight_end":41},{"text":"use tower_http::cors::CorsLayer;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `debug`, `error`, `info`, and `warn`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\endpoints.rs:17:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, warn, error, debug};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `config::ServerConfig`, `get_user_by_username`, `hash_password`, and `verify_password`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":461,"byte_end":481,"line_start":21,"line_end":21,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    config::ServerConfig,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":507,"byte_end":520,"line_start":22,"line_end":22,"column_start":25,"column_end":38,"is_primary":true,"text":[{"text":"    auth::{AuthService, hash_password, verify_password, get_user_by_username},","highlight_start":25,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":522,"byte_end":537,"line_start":22,"line_end":22,"column_start":40,"column_end":55,"is_primary":true,"text":[{"text":"    auth::{AuthService, hash_password, verify_password, get_user_by_username},","highlight_start":40,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":539,"byte_end":559,"line_start":22,"line_end":22,"column_start":57,"column_end":77,"is_primary":true,"text":[{"text":"    auth::{AuthService, hash_password, verify_password, get_user_by_username},","highlight_start":57,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":461,"byte_end":487,"line_start":21,"line_end":22,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    config::ServerConfig,","highlight_start":5,"highlight_end":26},{"text":"    auth::{AuthService, hash_password, verify_password, get_user_by_username},","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":505,"byte_end":559,"line_start":22,"line_end":22,"column_start":23,"column_end":77,"is_primary":true,"text":[{"text":"    auth::{AuthService, hash_password, verify_password, get_user_by_username},","highlight_start":23,"highlight_end":77}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":493,"byte_end":494,"line_start":22,"line_end":22,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"    auth::{AuthService, hash_password, verify_password, get_user_by_username},","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":559,"byte_end":560,"line_start":22,"line_end":22,"column_start":77,"column_end":78,"is_primary":true,"text":[{"text":"    auth::{AuthService, hash_password, verify_password, get_user_by_username},","highlight_start":77,"highlight_end":78}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `config::ServerConfig`, `get_user_by_username`, `hash_password`, and `verify_password`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\endpoints.rs:21:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config::ServerConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    auth::{AuthService, hash_password, verify_password, get_user_by_username},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `console` and `i18n::t`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":605,"byte_end":612,"line_start":25,"line_end":25,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"use fount_utils::{console, i18n::t};","highlight_start":19,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":614,"byte_end":621,"line_start":25,"line_end":25,"column_start":28,"column_end":35,"is_primary":true,"text":[{"text":"use fount_utils::{console, i18n::t};","highlight_start":28,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":587,"byte_end":624,"line_start":25,"line_end":26,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use fount_utils::{console, i18n::t};","highlight_start":1,"highlight_end":37},{"text":"use fount_types::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `console` and `i18n::t`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\endpoints.rs:25:19\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse fount_utils::{console, i18n::t};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `AiChatRequest`, `AiChatResponse`, `ApiResponse`, `CharacterListResponse`, `Character`, `CreateCharacterRequest`, `LoginResponse`, `PermissionLevel`, `Status`, `UpdateCharacterRequest`, and `User`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":647,"byte_end":656,"line_start":27,"line_end":27,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    Character, CreateCharacterRequest, UpdateCharacterRequest,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":658,"byte_end":680,"line_start":27,"line_end":27,"column_start":16,"column_end":38,"is_primary":true,"text":[{"text":"    Character, CreateCharacterRequest, UpdateCharacterRequest,","highlight_start":16,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":682,"byte_end":704,"line_start":27,"line_end":27,"column_start":40,"column_end":62,"is_primary":true,"text":[{"text":"    Character, CreateCharacterRequest, UpdateCharacterRequest,","highlight_start":40,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":710,"byte_end":731,"line_start":28,"line_end":28,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"    CharacterListResponse, ApiResponse, LoginRequest, LoginResponse,","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":733,"byte_end":744,"line_start":28,"line_end":28,"column_start":28,"column_end":39,"is_primary":true,"text":[{"text":"    CharacterListResponse, ApiResponse, LoginRequest, LoginResponse,","highlight_start":28,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":760,"byte_end":773,"line_start":28,"line_end":28,"column_start":55,"column_end":68,"is_primary":true,"text":[{"text":"    CharacterListResponse, ApiResponse, LoginRequest, LoginResponse,","highlight_start":55,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":779,"byte_end":792,"line_start":29,"line_end":29,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    AiChatRequest, AiChatResponse, User, PermissionLevel, Status,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":794,"byte_end":808,"line_start":29,"line_end":29,"column_start":20,"column_end":34,"is_primary":true,"text":[{"text":"    AiChatRequest, AiChatResponse, User, PermissionLevel, Status,","highlight_start":20,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":810,"byte_end":814,"line_start":29,"line_end":29,"column_start":36,"column_end":40,"is_primary":true,"text":[{"text":"    AiChatRequest, AiChatResponse, User, PermissionLevel, Status,","highlight_start":36,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":816,"byte_end":831,"line_start":29,"line_end":29,"column_start":42,"column_end":57,"is_primary":true,"text":[{"text":"    AiChatRequest, AiChatResponse, User, PermissionLevel, Status,","highlight_start":42,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":833,"byte_end":839,"line_start":29,"line_end":29,"column_start":59,"column_end":65,"is_primary":true,"text":[{"text":"    AiChatRequest, AiChatResponse, User, PermissionLevel, Status,","highlight_start":59,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":647,"byte_end":746,"line_start":27,"line_end":28,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    Character, CreateCharacterRequest, UpdateCharacterRequest,","highlight_start":5,"highlight_end":63},{"text":"    CharacterListResponse, ApiResponse, LoginRequest, LoginResponse,","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":758,"byte_end":839,"line_start":28,"line_end":29,"column_start":53,"column_end":65,"is_primary":true,"text":[{"text":"    CharacterListResponse, ApiResponse, LoginRequest, LoginResponse,","highlight_start":53,"highlight_end":69},{"text":"    AiChatRequest, AiChatResponse, User, PermissionLevel, Status,","highlight_start":1,"highlight_end":65}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":641,"byte_end":647,"line_start":26,"line_end":27,"column_start":18,"column_end":5,"is_primary":true,"text":[{"text":"use fount_types::{","highlight_start":18,"highlight_end":19},{"text":"    Character, CreateCharacterRequest, UpdateCharacterRequest,","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":839,"byte_end":842,"line_start":29,"line_end":30,"column_start":65,"column_end":2,"is_primary":true,"text":[{"text":"    AiChatRequest, AiChatResponse, User, PermissionLevel, Status,","highlight_start":65,"highlight_end":66},{"text":"};","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `AiChatRequest`, `AiChatResponse`, `ApiResponse`, `CharacterListResponse`, `Character`, `CreateCharacterRequest`, `LoginResponse`, `PermissionLevel`, `Status`, `UpdateCharacterRequest`, and `User`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\endpoints.rs:27:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Character, CreateCharacterRequest, UpdateCharacterRequest,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    CharacterListResponse, ApiResponse, LoginRequest, LoginResponse,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AiChatRequest, AiChatResponse, User, PermissionLevel, Status,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ConfigError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\setting_loader.rs","byte_start":93,"byte_end":104,"line_start":4,"line_end":4,"column_start":22,"column_end":33,"is_primary":true,"text":[{"text":"use config::{Config, ConfigError, Environment, File};","highlight_start":22,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\setting_loader.rs","byte_start":91,"byte_end":104,"line_start":4,"line_end":4,"column_start":20,"column_end":33,"is_primary":true,"text":[{"text":"use config::{Config, ConfigError, Environment, File};","highlight_start":20,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `ConfigError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\setting_loader.rs:4:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse config::{Config, ConfigError, Environment, File};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UpdateAiSourceRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\ai_sources_manager.rs","byte_start":207,"byte_end":228,"line_start":7,"line_end":7,"column_start":58,"column_end":79,"is_primary":true,"text":[{"text":"use fount_types::{AiSourceConfig, CreateAiSourceRequest, UpdateAiSourceRequest, AiChatRequest, AiChatResponse};","highlight_start":58,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\ai_sources_manager.rs","byte_start":205,"byte_end":228,"line_start":7,"line_end":7,"column_start":56,"column_end":79,"is_primary":true,"text":[{"text":"use fount_types::{AiSourceConfig, CreateAiSourceRequest, UpdateAiSourceRequest, AiChatRequest, AiChatResponse};","highlight_start":56,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UpdateAiSourceRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\ai_sources_manager.rs:7:58\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse fount_types::{AiSourceConfig, CreateAiSourceRequest, UpdateAiSourceRequest, AiChatRequest, AiChatResponse};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":234,"byte_end":249,"line_start":11,"line_end":11,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"pub use char_manager::*;","highlight_start":9,"highlight_end":24}],"label":"the name `init` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":340,"byte_end":361,"line_start":15,"line_end":15,"column_start":9,"column_end":30,"is_primary":false,"text":[{"text":"pub use ai_sources_manager::*;","highlight_start":9,"highlight_end":30}],"label":"but the name `init` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(ambiguous_glob_reexports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\mod.rs:11:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use char_manager::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mthe name `init` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use ai_sources_manager::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbut the name `init` in the value namespace is also re-exported here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(ambiguous_glob_reexports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":388,"byte_end":413,"line_start":18,"line_end":18,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":384,"byte_end":415,"line_start":18,"line_end":19,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\mod.rs:18:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::sync::Arc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":419,"byte_end":433,"line_start":19,"line_end":19,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":415,"byte_end":435,"line_start":19,"line_end":20,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":20},{"text":"use tokio::sync::RwLock;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::sync::Arc`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\mod.rs:19:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::sync::RwLock`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":439,"byte_end":458,"line_start":20,"line_end":20,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use tokio::sync::RwLock;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":435,"byte_end":460,"line_start":20,"line_end":21,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::sync::RwLock;","highlight_start":1,"highlight_end":25},{"text":"use tracing::{info, warn, error, debug};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tokio::sync::RwLock`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\mod.rs:20:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::sync::RwLock;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `debug`, `error`, and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":480,"byte_end":484,"line_start":21,"line_end":21,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":21,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":486,"byte_end":491,"line_start":21,"line_end":21,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":493,"byte_end":498,"line_start":21,"line_end":21,"column_start":34,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":34,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":478,"byte_end":498,"line_start":21,"line_end":21,"column_start":19,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":19,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":473,"byte_end":474,"line_start":21,"line_end":21,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates\\fount-server\\src\\managers\\mod.rs","byte_start":498,"byte_end":499,"line_start":21,"line_end":21,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, debug};","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `debug`, `error`, and `warn`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\mod.rs:21:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, warn, error, debug};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\lib.rs","byte_start":593,"byte_end":602,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use server::*;","highlight_start":9,"highlight_end":18}],"label":"the name `health_check` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\lib.rs","byte_start":629,"byte_end":641,"line_start":33,"line_end":33,"column_start":9,"column_end":21,"is_primary":false,"text":[{"text":"pub use endpoints::*;","highlight_start":9,"highlight_end":21}],"label":"but the name `health_check` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\lib.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use server::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mthe name `health_check` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use auth::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use endpoints::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbut the name `health_check` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\lib.rs","byte_start":593,"byte_end":602,"line_start":31,"line_end":31,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use server::*;","highlight_start":9,"highlight_end":18}],"label":"the name `init` in the value namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\lib.rs","byte_start":821,"byte_end":832,"line_start":42,"line_end":42,"column_start":9,"column_end":20,"is_primary":false,"text":[{"text":"pub use managers::*;","highlight_start":9,"highlight_end":20}],"label":"but the name `init` in the value namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\lib.rs:31:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use server::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mthe name `init` in the value namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use managers::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbut the name `init` in the value namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\lib.rs","byte_start":670,"byte_end":676,"line_start":35,"line_end":35,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"pub use ipc::*;","highlight_start":9,"highlight_end":15}],"label":"the name `IpcResponse` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\fount-server\\src\\lib.rs","byte_start":686,"byte_end":699,"line_start":36,"line_end":36,"column_start":9,"column_end":22,"is_primary":false,"text":[{"text":"pub use ipc_server::*;","highlight_start":9,"highlight_end":22}],"label":"but the name `IpcResponse` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\lib.rs:35:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use ipc::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mthe name `IpcResponse` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use ipc_server::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbut the name `IpcResponse` in the type namespace is also re-exported here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":2417,"byte_end":2422,"line_start":75,"line_end":75,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<AppState>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\endpoints.rs","byte_start":2417,"byte_end":2422,"line_start":75,"line_end":75,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<AppState>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `state`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\endpoints.rs:75:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m75\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<AppState>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\shell_manager.rs","byte_start":598,"byte_end":605,"line_start":26,"line_end":26,"column_start":39,"column_end":46,"is_primary":true,"text":[{"text":"    pub async fn execute_shell(&self, request: ShellExecuteRequest) -> Result<String> {","highlight_start":39,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\shell_manager.rs","byte_start":598,"byte_end":605,"line_start":26,"line_end":26,"column_start":39,"column_end":46,"is_primary":true,"text":[{"text":"    pub async fn execute_shell(&self, request: ShellExecuteRequest) -> Result<String> {","highlight_start":39,"highlight_end":46}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `request`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\shell_manager.rs:26:39\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn execute_shell(&self, request: ShellExecuteRequest) -> Result<String> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\ai_sources_manager.rs","byte_start":1518,"byte_end":1525,"line_start":48,"line_end":48,"column_start":30,"column_end":37,"is_primary":true,"text":[{"text":"    pub async fn chat(&self, request: AiChatRequest) -> Result<AiChatResponse> {","highlight_start":30,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\ai_sources_manager.rs","byte_start":1518,"byte_end":1525,"line_start":48,"line_end":48,"column_start":30,"column_end":37,"is_primary":true,"text":[{"text":"    pub async fn chat(&self, request: AiChatRequest) -> Result<AiChatResponse> {","highlight_start":30,"highlight_end":37}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `request`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\ai_sources_manager.rs:48:30\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn chat(&self, request: AiChatRequest) -> Result<AiChatResponse> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\char_manager.rs","byte_start":5438,"byte_end":5446,"line_start":165,"line_end":165,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"    async fn get_part_list(&self, username: &str) -> Result<Vec<String>> {","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\char_manager.rs","byte_start":5438,"byte_end":5446,"line_start":165,"line_end":165,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"    async fn get_part_list(&self, username: &str) -> Result<Vec<String>> {","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\char_manager.rs:165:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m165\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_part_list(&self, username: &str) -> Result<Vec<String>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\char_manager.rs","byte_start":5748,"byte_end":5756,"line_start":174,"line_end":174,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\char_manager.rs","byte_start":5748,"byte_end":5756,"line_start":174,"line_end":174,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\char_manager.rs:174:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m174\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `no_cache`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\char_manager.rs","byte_start":5776,"byte_end":5784,"line_start":174,"line_end":174,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\char_manager.rs","byte_start":5776,"byte_end":5784,"line_start":174,"line_end":174,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":"_no_cache","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `no_cache`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\char_manager.rs:174:66\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m174\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_no_cache`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\shell_manager.rs","byte_start":1233,"byte_end":1241,"line_start":52,"line_end":52,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"    async fn get_part_list(&self, username: &str) -> Result<Vec<String>> {","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\shell_manager.rs","byte_start":1233,"byte_end":1241,"line_start":52,"line_end":52,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"    async fn get_part_list(&self, username: &str) -> Result<Vec<String>> {","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\shell_manager.rs:52:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m52\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_part_list(&self, username: &str) -> Result<Vec<String>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\shell_manager.rs","byte_start":1529,"byte_end":1537,"line_start":61,"line_end":61,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\shell_manager.rs","byte_start":1529,"byte_end":1537,"line_start":61,"line_end":61,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\shell_manager.rs:61:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `no_cache`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\shell_manager.rs","byte_start":1557,"byte_end":1565,"line_start":61,"line_end":61,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\shell_manager.rs","byte_start":1557,"byte_end":1565,"line_start":61,"line_end":61,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":"_no_cache","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `no_cache`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\shell_manager.rs:61:66\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_no_cache`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\personas_manager.rs","byte_start":1572,"byte_end":1580,"line_start":68,"line_end":68,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"    async fn get_part_list(&self, username: &str) -> anyhow::Result<Vec<String>> {","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\personas_manager.rs","byte_start":1572,"byte_end":1580,"line_start":68,"line_end":68,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"    async fn get_part_list(&self, username: &str) -> anyhow::Result<Vec<String>> {","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\personas_manager.rs:68:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_part_list(&self, username: &str) -> anyhow::Result<Vec<String>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\personas_manager.rs","byte_start":1884,"byte_end":1892,"line_start":77,"line_end":77,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\personas_manager.rs","byte_start":1884,"byte_end":1892,"line_start":77,"line_end":77,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\personas_manager.rs:77:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0metails(&self, username: &str, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `no_cache`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\personas_manager.rs","byte_start":1912,"byte_end":1920,"line_start":77,"line_end":77,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\personas_manager.rs","byte_start":1912,"byte_end":1920,"line_start":77,"line_end":77,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":"_no_cache","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `no_cache`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\personas_manager.rs:77:66\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_no_cache`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\world_manager.rs","byte_start":1540,"byte_end":1548,"line_start":62,"line_end":62,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"    async fn get_part_list(&self, username: &str) -> anyhow::Result<Vec<String>> {","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\world_manager.rs","byte_start":1540,"byte_end":1548,"line_start":62,"line_end":62,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"    async fn get_part_list(&self, username: &str) -> anyhow::Result<Vec<String>> {","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\world_manager.rs:62:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m62\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_part_list(&self, username: &str) -> anyhow::Result<Vec<String>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\world_manager.rs","byte_start":1846,"byte_end":1854,"line_start":71,"line_end":71,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\world_manager.rs","byte_start":1846,"byte_end":1854,"line_start":71,"line_end":71,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\world_manager.rs:71:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0metails(&self, username: &str, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `no_cache`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\world_manager.rs","byte_start":1874,"byte_end":1882,"line_start":71,"line_end":71,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\world_manager.rs","byte_start":1874,"byte_end":1882,"line_start":71,"line_end":71,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":"_no_cache","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `no_cache`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\world_manager.rs:71:66\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_no_cache`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\ai_sources_manager.rs","byte_start":2575,"byte_end":2583,"line_start":86,"line_end":86,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"    async fn get_part_list(&self, username: &str) -> Result<Vec<String>> {","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\ai_sources_manager.rs","byte_start":2575,"byte_end":2583,"line_start":86,"line_end":86,"column_start":35,"column_end":43,"is_primary":true,"text":[{"text":"    async fn get_part_list(&self, username: &str) -> Result<Vec<String>> {","highlight_start":35,"highlight_end":43}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\ai_sources_manager.rs:86:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_part_list(&self, username: &str) -> Result<Vec<String>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\ai_sources_manager.rs","byte_start":2874,"byte_end":2882,"line_start":95,"line_end":95,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\ai_sources_manager.rs","byte_start":2874,"byte_end":2882,"line_start":95,"line_end":95,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\ai_sources_manager.rs:95:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m95\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `no_cache`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\managers\\ai_sources_manager.rs","byte_start":2902,"byte_end":2910,"line_start":95,"line_end":95,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates\\fount-server\\src\\managers\\ai_sources_manager.rs","byte_start":2902,"byte_end":2910,"line_start":95,"line_end":95,"column_start":66,"column_end":74,"is_primary":true,"text":[{"text":"    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {","highlight_start":66,"highlight_end":74}],"label":null,"suggested_replacement":"_no_cache","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `no_cache`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\managers\\ai_sources_manager.rs:95:66\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m95\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_no_cache`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACCOUNT_LOCK_TIME` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":2497,"byte_end":2514,"line_start":79,"line_end":79,"column_start":7,"column_end":24,"is_primary":true,"text":[{"text":"const ACCOUNT_LOCK_TIME: i64 = 10 * 60; // 10分钟","highlight_start":7,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: constant `ACCOUNT_LOCK_TIME` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\auth.rs:79:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m79\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst ACCOUNT_LOCK_TIME: i64 = 10 * 60; // 10分钟\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `MAX_LOGIN_ATTEMPTS` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":2549,"byte_end":2567,"line_start":80,"line_end":80,"column_start":7,"column_end":25,"is_primary":true,"text":[{"text":"const MAX_LOGIN_ATTEMPTS: u32 = 5;","highlight_start":7,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: constant `MAX_LOGIN_ATTEMPTS` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\auth.rs:80:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst MAX_LOGIN_ATTEMPTS: u32 = 5;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `save_auth_data` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\auth.rs","byte_start":4688,"byte_end":4702,"line_start":155,"line_end":155,"column_start":4,"column_end":18,"is_primary":true,"text":[{"text":"fn save_auth_data() -> Result<()> {","highlight_start":4,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `save_auth_data` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\auth.rs:155:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m155\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn save_auth_data() -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"comparison is useless due to type limits","code":{"code":"unused_comparisons","explanation":null},"level":"warning","spans":[{"file_name":"crates\\fount-server\\src\\setting_loader.rs","byte_start":5415,"byte_end":5443,"line_start":177,"line_end":177,"column_start":41,"column_end":69,"is_primary":true,"text":[{"text":"        if settings.server.port == 0 || settings.server.port > 65535 {","highlight_start":41,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_comparisons)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: comparison is useless due to type limits\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\fount-server\\src\\setting_loader.rs:177:41\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if settings.server.port == 0 || settings.server.port > 65535 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_comparisons)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"50 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 50 warnings emitted\u001b[0m\n\n"}
