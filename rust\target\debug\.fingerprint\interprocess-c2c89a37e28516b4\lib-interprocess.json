{"rustc": 12488743700189009532, "features": "[\"blocking\", \"default\", \"futures-core\", \"futures-io\", \"intmap\", \"nonblocking\", \"once_cell\", \"signals\", \"spinning\", \"thiserror\"]", "declared_features": "[\"blocking\", \"default\", \"doc_cfg\", \"futures-core\", \"futures-io\", \"intmap\", \"nonblocking\", \"once_cell\", \"signals\", \"spinning\", \"thiserror\", \"tokio\", \"tokio_support\"]", "target": 15901425574398377358, "profile": 5347358027863023418, "path": 10479453526534412440, "deps": [[5103565458935487, "futures_io", false, 3426668124418015326], [413895055751620470, "to_method", false, 17625868506913754781], [2924422107542798392, "libc", false, 11810293736146394747], [3722963349756955755, "once_cell", false, 13898173386827918274], [7620660491849607393, "futures_core", false, 15375766359717064194], [8008191657135824715, "thiserror", false, 16604617059829458511], [10020888071089587331, "<PERSON>ap<PERSON>", false, 16413055895575873994], [10411997081178400487, "cfg_if", false, 2302665342036146008], [12944474953899598757, "blocking", false, 1110625533203509128], [13564245402892897462, "spinning", false, 7589005943318051551], [15344998369347094209, "build_script_build", false, 15060651889381965943], [17329584784123403638, "intmap", false, 5235022365563196152]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\interprocess-c2c89a37e28516b4\\dep-lib-interprocess", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}