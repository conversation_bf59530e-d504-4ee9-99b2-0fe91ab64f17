{"rustc": 12488743700189009532, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 18330098564635666122, "path": 9106225109925950334, "deps": [[1213098572879462490, "json5_rs", false, 4749566955863867098], [1965680986145237447, "yaml_rust2", false, 13589526178912196101], [2244620803250265856, "ron", false, 14406694876059500463], [6502365400774175331, "nom", false, 14910136683565223575], [6517602928339163454, "path<PERSON><PERSON>", false, 16131452501056481094], [8786711029710048183, "toml", false, 11676747463454101134], [9689903380558560274, "serde", false, 18066533925557853048], [11946729385090170470, "async_trait", false, 6645668793843756460], [13475460906694513802, "convert_case", false, 4831696312440337529], [14618892375165583068, "ini", false, 13797206070096345684], [15367738274754116744, "serde_json", false, 11959657437500097803]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\config-e55b858ca8d00b3c\\dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}