{"rustc": 12488743700189009532, "features": "[\"default\", \"rustls-tls\", \"zip\"]", "declared_features": "[\"default\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"zip\"]", "target": 5081136802505358982, "profile": 8276155916380437441, "path": 6899552628466203349, "deps": [[40386456601120721, "percent_encoding", false, 11201700476752682152], [889364689474263285, "reqwest", false, 8649252153039804776], [1441306149310335789, "tempfile", false, 10669665937460083339], [3150220818285335163, "url", false, 10260461479629395240], [4899080583175475170, "semver", false, 16908758147680727457], [5986029879202738730, "log", false, 7320443587293871305], [9010263965687315507, "http", false, 14438606696231830319], [9332307739160395223, "minisign_verify", false, 3431376495486690893], [9538054652646069845, "tokio", false, 8619070681005270242], [9689903380558560274, "serde", false, 7175605069985593048], [10281541584571964250, "windows_sys", false, 16133675991762894175], [10629569228670356391, "futures_util", false, 1180256261339817079], [10755362358622467486, "tauri", false, 4877919634967005350], [10806645703491011684, "thiserror", false, 14203071536756777833], [11721252211900136025, "build_script_build", false, 600530348092220004], [12409575957772518135, "time", false, 7015746202650124867], [13077212702700853852, "base64", false, 13497128256263695626], [15367738274754116744, "serde_json", false, 1417905963242708491], [17146114186171651583, "infer", false, 14129838665284832961], [18372475104564266000, "zip", false, 6654991509949738339]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-updater-91bea3b425845895\\dep-lib-tauri_plugin_updater", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}