{"rustc": 12488743700189009532, "features": "[\"basic-toml\", \"config\", \"humansize\", \"num-traits\", \"serde\", \"urlencode\", \"with-axum\"]", "declared_features": "[\"basic-toml\", \"config\", \"humansize\", \"markdown\", \"num-traits\", \"serde\", \"serde-json\", \"serde-yaml\", \"urlencode\", \"with-actix-web\", \"with-axum\", \"with-gotham\", \"with-hyper\", \"with-mendes\", \"with-rocket\", \"with-tide\", \"with-warp\"]", "target": 16002229835656363085, "profile": 3033921117576893, "path": 7324276108626717239, "deps": [[3060637413840920116, "proc_macro2", false, 861585713616588544], [8535110969670198065, "parser", false, 14194618796172758337], [9083217064824284637, "basic_toml", false, 2459917641754353292], [9689903380558560274, "serde", false, 2677210708066591388], [10229185211513642314, "mime", false, 1930215642508730926], [17990358020177143287, "quote", false, 5123393557542164599], [18071510856783138481, "mime_guess", false, 4086097355871731076], [18149961000318489080, "syn", false, 14168920756897380641]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\askama_derive-531689713680376b\\dep-lib-askama_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}