{"rustc": 12488743700189009532, "features": "[\"default\", \"form\", \"http1\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 5347358027863023418, "path": 1047557593175177085, "deps": [[40386456601120721, "percent_encoding", false, 9012493762664266909], [778154619793643451, "hyper_util", false, 18245025738968059108], [784494742817713399, "tower_service", false, 3682681021102183911], [1906322745568073236, "pin_project_lite", false, 4734008351949582595], [2517136641825875337, "sync_wrapper", false, 11622035070159097558], [3129130049864710036, "memchr", false, 17749649384327466550], [4359148418957042248, "axum_core", false, 1907618875155142613], [5695049318159433696, "tower", false, 10192046654142321829], [6328167575312831016, "tokio_tungstenite", false, 17592324172280573636], [7695812897323945497, "itoa", false, 10210797905631196015], [7712452662827335977, "tower_layer", false, 10995423915119558615], [7858942147296547339, "rustversion", false, 1948865793622661436], [7940089053034940860, "axum_macros", false, 2973763310331131202], [8606274917505247608, "tracing", false, 11172206976261258179], [9010263965687315507, "http", false, 3756583827281232345], [9538054652646069845, "tokio", false, 1845311123818853277], [9678799920983747518, "matchit", false, 5919554849643943670], [9689903380558560274, "serde", false, 18066533925557853048], [10229185211513642314, "mime", false, 1930215642508730926], [10629569228670356391, "futures_util", false, 17733622655136772691], [10724389056617919257, "sha1", false, 10870411682345686423], [11946729385090170470, "async_trait", false, 6645668793843756460], [11957360342995674422, "hyper", false, 16162904395828383064], [12757619235593077227, "multer", false, 13297932478158181567], [13077212702700853852, "base64", false, 7372439776013991939], [13645307863515715290, "serde_path_to_error", false, 9124276419007021738], [14084095096285906100, "http_body", false, 185161320294960308], [15367738274754116744, "serde_json", false, 11959657437500097803], [16066129441945555748, "bytes", false, 4582155335073800466], [16542808166767769916, "serde_urlencoded", false, 8093079612871783480], [16900715236047033623, "http_body_util", false, 6133363444511603795]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-05b381add94d5baa\\dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}