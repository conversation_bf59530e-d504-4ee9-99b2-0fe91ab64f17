{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17932638827197247375, "build_script_build", false, 11544144704476837760], [10755362358622467486, "build_script_build", false, 15889313533795488443], [3834743577069889284, "build_script_build", false, 15934251066969651894], [13890802266741835355, "build_script_build", false, 13213058633667705622], [12783828711503588811, "build_script_build", false, 18223115693062972846], [1582828171158827377, "build_script_build", false, 12066620190564086045], [11721252211900136025, "build_script_build", false, 600530348092220004], [16445525635439251275, "build_script_build", false, 1467413259275981720]], "local": [{"RerunIfChanged": {"output": "debug\\build\\fount-desktop-22ac5136a08dfd8e\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}