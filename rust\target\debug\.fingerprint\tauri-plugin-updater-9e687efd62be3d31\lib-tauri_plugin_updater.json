{"rustc": 12488743700189009532, "features": "[\"default\", \"rustls-tls\", \"zip\"]", "declared_features": "[\"default\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"zip\"]", "target": 5081136802505358982, "profile": 5347358027863023418, "path": 6899552628466203349, "deps": [[40386456601120721, "percent_encoding", false, 9012493762664266909], [889364689474263285, "reqwest", false, 10192670632514332786], [1441306149310335789, "tempfile", false, 7393926511970484316], [3150220818285335163, "url", false, 15730960352794477868], [4899080583175475170, "semver", false, 9825423997313627795], [5986029879202738730, "log", false, 15132848699422293037], [9010263965687315507, "http", false, 3756583827281232345], [9332307739160395223, "minisign_verify", false, 3222063269594609814], [9538054652646069845, "tokio", false, 1845311123818853277], [9689903380558560274, "serde", false, 18066533925557853048], [10281541584571964250, "windows_sys", false, 16185763519949114385], [10629569228670356391, "futures_util", false, 17733622655136772691], [10755362358622467486, "tauri", false, 10248244220009135351], [10806645703491011684, "thiserror", false, 15657458092141047087], [11721252211900136025, "build_script_build", false, 15081921822872552394], [12409575957772518135, "time", false, 5869925475327372535], [13077212702700853852, "base64", false, 7372439776013991939], [15367738274754116744, "serde_json", false, 11959657437500097803], [17146114186171651583, "infer", false, 293705839189906348], [18372475104564266000, "zip", false, 12512002901074352466]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-updater-9e687efd62be3d31\\dep-lib-tauri_plugin_updater", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}