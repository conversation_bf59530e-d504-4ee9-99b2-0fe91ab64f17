{"rustc": 12488743700189009532, "features": "[\"backtrace\", \"contexts\", \"debug-images\", \"default\", \"httpdate\", \"native-tls\", \"panic\", \"reqwest\", \"sentry-backtrace\", \"sentry-contexts\", \"sentry-debug-images\", \"sentry-panic\", \"sentry-tracing\", \"tokio\", \"tracing\", \"transport\"]", "declared_features": "[\"UNSTABLE_metrics\", \"anyhow\", \"backtrace\", \"contexts\", \"curl\", \"debug-images\", \"debug-logs\", \"default\", \"http-client\", \"httpdate\", \"isahc\", \"log\", \"native-tls\", \"panic\", \"reqwest\", \"rustls\", \"sentry-anyhow\", \"sentry-backtrace\", \"sentry-contexts\", \"sentry-debug-images\", \"sentry-log\", \"sentry-panic\", \"sentry-slog\", \"sentry-tower\", \"sentry-tracing\", \"serde_json\", \"slog\", \"surf\", \"surf-h1\", \"test\", \"tokio\", \"tower\", \"tower-axum-matched-path\", \"tower-http\", \"tracing\", \"transport\", \"ureq\", \"webpki-roots\"]", "target": 1678334649273565773, "profile": 5347358027863023418, "path": 5709427884395948961, "deps": [[889364689474263285, "reqwest", false, 10192670632514332786], [994385991029174609, "sentry_tracing", false, 15109938736203791708], [6304235478050270880, "httpdate", false, 1941731310534099401], [6564039303567649966, "sentry_core", false, 6369271220739919288], [9538054652646069845, "tokio", false, 1845311123818853277], [11308626530065046827, "sentry_panic", false, 3975917839608409396], [13930065473154359735, "sentry_debug_images", false, 17032769149985095994], [14452548244456819072, "sentry_backtrace", false, 16575760420824823794], [16785601910559813697, "native_tls", false, 6273308036569105457], [17745100799483310686, "sentry_contexts", false, 9756112983863687843]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sentry-beb786313946faea\\dep-lib-sentry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}