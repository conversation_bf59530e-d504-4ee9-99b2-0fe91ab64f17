import struct

# ICO header: reserved(2) + type(2) + count(2)
header = struct.pack('<HHH', 0, 1, 1)

# Directory entry: width(1) + height(1) + colors(1) + reserved(1) + planes(2) + bpp(2) + size(4) + offset(4)
entry = struct.pack('<BBBBHHII', 16, 16, 0, 0, 1, 32, 1088, 22)

# BMP header for 16x16 32-bit image
bmp_header = struct.pack('<IIIHHIIIIII', 40, 16, 32, 1, 32, 0, 1024, 0, 0, 0, 0)

# 16x16 pixels, 4 bytes each (BGRA), all transparent
pixels = b'\x00' * 1024

# Write ICO file
with open('icons/icon.ico', 'wb') as f:
    f.write(header + entry + bmp_header + pixels)

print('Created minimal icon.ico')
