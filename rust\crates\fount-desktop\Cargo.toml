[package]
name = "fount-desktop"
version = "0.1.0"
edition = "2021"
description = "Fount desktop application"
license = "MIT"
authors = ["Fount Team"]
repository = "https://github.com/steve02081504/fount"

[build-dependencies]
tauri-build = { version = "2.2.0", features = [] }

[dependencies]
# 内部依赖
fount-server = { workspace = true }
fount-types = { workspace = true }
fount-utils = { workspace = true }

# Tauri
tauri = { version = "2.5.1", features = ["tray-icon"] }
tauri-plugin-shell = "2.0.0"
tauri-plugin-dialog = "2.0.0"
tauri-plugin-fs = "2.0.0"
tauri-plugin-notification = "2.0.0"
tauri-plugin-updater = "2.0.0"
tauri-plugin-window-state = "2.0.0"

# 核心
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }

# 日志
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

# 系统集成
tray-icon = { workspace = true }
single-instance = "0.3.0"

# 异步
async-trait = { workspace = true }
futures = { workspace = true }

# 配置
dirs = { workspace = true }
config = { workspace = true }

# 时间
chrono = { workspace = true }

# 网络
reqwest = { workspace = true }

# 文件系统
fs_extra = { workspace = true }

# UUID
uuid = { workspace = true }
