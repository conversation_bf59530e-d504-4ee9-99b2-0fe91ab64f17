{"rustc": 12488743700189009532, "features": "[\"config\", \"default\", \"dep_humansize\", \"dep_num_traits\", \"humansize\", \"mime\", \"mime_guess\", \"num-traits\", \"percent-encoding\", \"urlencode\", \"with-axum\"]", "declared_features": "[\"comrak\", \"config\", \"default\", \"dep_humansize\", \"dep_num_traits\", \"humansize\", \"markdown\", \"mime\", \"mime_guess\", \"num-traits\", \"percent-encoding\", \"serde\", \"serde-json\", \"serde-yaml\", \"serde_json\", \"serde_yaml\", \"urlencode\", \"with-actix-web\", \"with-axum\", \"with-gotham\", \"with-hyper\", \"with-mendes\", \"with-rocket\", \"with-tide\", \"with-warp\"]", "target": 13506025875297133661, "profile": 5347358027863023418, "path": 7888821292479115785, "deps": [[40386456601120721, "percent_encoding", false, 9012493762664266909], [5157631553186200874, "dep_num_traits", false, 1511456502098505525], [6593674146359544692, "dep_humansize", false, 1616313439458243996], [7209081853818273543, "askama_derive", false, 14695890933179310569], [13006376796596721141, "askama_escape", false, 1984849791986431629]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\askama-5c33ccb2e7dde1c2\\dep-lib-askama", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}