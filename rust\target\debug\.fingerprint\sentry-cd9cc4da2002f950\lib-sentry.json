{"rustc": 12488743700189009532, "features": "[\"backtrace\", \"contexts\", \"debug-images\", \"default\", \"httpdate\", \"native-tls\", \"panic\", \"reqwest\", \"sentry-backtrace\", \"sentry-contexts\", \"sentry-debug-images\", \"sentry-panic\", \"sentry-tracing\", \"tokio\", \"tracing\", \"transport\"]", "declared_features": "[\"UNSTABLE_metrics\", \"anyhow\", \"backtrace\", \"contexts\", \"curl\", \"debug-images\", \"debug-logs\", \"default\", \"http-client\", \"httpdate\", \"isahc\", \"log\", \"native-tls\", \"panic\", \"reqwest\", \"rustls\", \"sentry-anyhow\", \"sentry-backtrace\", \"sentry-contexts\", \"sentry-debug-images\", \"sentry-log\", \"sentry-panic\", \"sentry-slog\", \"sentry-tower\", \"sentry-tracing\", \"serde_json\", \"slog\", \"surf\", \"surf-h1\", \"test\", \"tokio\", \"tower\", \"tower-axum-matched-path\", \"tower-http\", \"tracing\", \"transport\", \"ureq\", \"webpki-roots\"]", "target": 1678334649273565773, "profile": 5347358027863023418, "path": 5709427884395948961, "deps": [[889364689474263285, "reqwest", false, 3251926945658722897], [994385991029174609, "sentry_tracing", false, 12587582177580439500], [6304235478050270880, "httpdate", false, 1941731310534099401], [6564039303567649966, "sentry_core", false, 11910817496472911207], [9538054652646069845, "tokio", false, 8242399167134721697], [11308626530065046827, "sentry_panic", false, 16964101239468101379], [13930065473154359735, "sentry_debug_images", false, 3373751396313728043], [14452548244456819072, "sentry_backtrace", false, 13083126485128228494], [16785601910559813697, "native_tls", false, 11119506298119515743], [17745100799483310686, "sentry_contexts", false, 694613148874502120]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sentry-cd9cc4da2002f950\\dep-lib-sentry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}