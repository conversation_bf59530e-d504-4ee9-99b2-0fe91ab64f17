{"rustc": 12488743700189009532, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 1006155289083248400, "path": 7872416515397446104, "deps": [[1009387600818341822, "matchers", false, 11600866143001751714], [1017461770342116999, "sharded_slab", false, 15561746169831252574], [3722963349756955755, "once_cell", false, 13898173386827918274], [6048213226671835012, "smallvec", false, 16708652725310964977], [6981130804689348050, "tracing_serde", false, 913361435372933930], [8606274917505247608, "tracing", false, 1145649429643657630], [8614575489689151157, "nu_ansi_term", false, 10719523165610948150], [9451456094439810778, "regex", false, 15679568338494819172], [9689903380558560274, "serde", false, 2677210708066591388], [10806489435541507125, "tracing_log", false, 4734062893111861312], [11033263105862272874, "tracing_core", false, 16968279864401715728], [12427285511609802057, "thread_local", false, 12280964067965948498], [15367738274754116744, "serde_json", false, 1472342806914326034]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-116d96ab0e22badf\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}