{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "Fount", "version": "0.1.0", "identifier": "com.fount.app", "build": {"beforeDevCommand": "", "beforeBuildCommand": "", "devUrl": "http://localhost:3000", "frontendDist": "../static"}, "app": {"windows": [{"fullscreen": false, "resizable": true, "title": "Fount", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}], "security": {"csp": null}}, "bundle": {"active": false, "targets": "all", "icon": []}, "plugins": {"shell": {"open": true}, "dialog": {"open": true, "save": true}, "fs": {"readFile": true, "writeFile": true, "readDir": true, "createDir": true, "removeDir": true, "removeFile": true, "exists": true}, "notification": {"all": true}}}