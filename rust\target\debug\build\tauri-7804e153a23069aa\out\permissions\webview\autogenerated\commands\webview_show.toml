# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-webview-show"
description = "Enables the webview_show command without any pre-configured scope."
commands.allow = ["webview_show"]

[[permission]]
identifier = "deny-webview-show"
description = "Denies the webview_show command without any pre-configured scope."
commands.deny = ["webview_show"]
