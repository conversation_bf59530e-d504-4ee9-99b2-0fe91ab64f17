{"rustc": 12488743700189009532, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 18330098564635666122, "path": 9106225109925950334, "deps": [[1213098572879462490, "json5_rs", false, 16761558264373057844], [1965680986145237447, "yaml_rust2", false, 13589526178912196101], [2244620803250265856, "ron", false, 17002242853609722728], [6502365400774175331, "nom", false, 14910136683565223575], [6517602928339163454, "path<PERSON><PERSON>", false, 16131452501056481094], [8786711029710048183, "toml", false, 16685973249132342939], [9689903380558560274, "serde", false, 2677210708066591388], [11946729385090170470, "async_trait", false, 6969772722108267470], [13475460906694513802, "convert_case", false, 4831696312440337529], [14618892375165583068, "ini", false, 10923729747962096772], [15367738274754116744, "serde_json", false, 1472342806914326034]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\config-d521631fb4acd753\\dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}