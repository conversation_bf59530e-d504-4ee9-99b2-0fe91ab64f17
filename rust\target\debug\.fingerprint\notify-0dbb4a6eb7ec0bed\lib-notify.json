{"rustc": 12488743700189009532, "features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"macos_fsevent\"]", "declared_features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"kqueue\", \"macos_fsevent\", \"macos_kqueue\", \"manual_tests\", \"mio\", \"serde\", \"timing_tests\"]", "target": 4487759779636071210, "profile": 5347358027863023418, "path": 13842500022749034100, "deps": [[1999565553139417705, "windows_sys", false, 15637157833458489253], [2924422107542798392, "libc", false, 11810293736146394747], [3869670940427635694, "filetime", false, 6108851128529306957], [5986029879202738730, "log", false, 15132848699422293037], [9727213718512686088, "crossbeam_channel", false, 6382223421897143473], [15622660310229662834, "walkdir", false, 15602106002375009206]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\notify-0dbb4a6eb7ec0bed\\dep-lib-notify", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}