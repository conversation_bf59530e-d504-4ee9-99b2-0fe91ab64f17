{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 15889313533795488443], [11721252211900136025, "build_script_build", false, 17873866453876282860]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-updater-f3a2f044af251fbf\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}