{"rustc": 12488743700189009532, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 5347358027863023418, "path": 13456413559598284403, "deps": [[99287295355353247, "data_encoding", false, 6706084113399466547], [3150220818285335163, "url", false, 114805819454090885], [3712811570531045576, "byteorder", false, 315979521691986690], [4359956005902820838, "utf8", false, 10854213729487511928], [5986029879202738730, "log", false, 15132848699422293037], [6163892036024256188, "httparse", false, 4094545348164024979], [8008191657135824715, "thiserror", false, 14582084796005058305], [9010263965687315507, "http", false, 3756583827281232345], [10724389056617919257, "sha1", false, 10870411682345686423], [13208667028893622512, "rand", false, 10855335974488221922], [16066129441945555748, "bytes", false, 4582155335073800466]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tungstenite-f362f65d94f71d46\\dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}