{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 5347358027863023418, "path": 8694853403362143863, "deps": [[947818755262499932, "notify_rust", false, 12457060807942966560], [3150220818285335163, "url", false, 15730960352794477868], [5986029879202738730, "log", false, 15132848699422293037], [9689903380558560274, "serde", false, 18066533925557853048], [10755362358622467486, "tauri", false, 10248244220009135351], [10806645703491011684, "thiserror", false, 15657458092141047087], [12409575957772518135, "time", false, 5869925475327372535], [12783828711503588811, "build_script_build", false, 4165053365432618229], [12986574360607194341, "serde_repr", false, 8271347498386200132], [13208667028893622512, "rand", false, 10855335974488221922], [15367738274754116744, "serde_json", false, 11959657437500097803]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-notification-b46d0bcc7a6132bc\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}