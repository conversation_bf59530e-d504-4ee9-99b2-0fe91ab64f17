<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fount Desktop App</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            margin: 10px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .feature-list {
            text-align: left;
            margin: 20px 0;
        }
        .feature-list li {
            margin: 10px 0;
            padding: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Fount Desktop</h1>
        <p>AI Character Chat Platform - Desktop Application</p>
        
        <div class="status" id="status">
            <strong>Status:</strong> <span id="server-status">Checking...</span>
        </div>
        
        <div>
            <button onclick="startServer()">Start Server</button>
            <button onclick="stopServer()">Stop Server</button>
            <button onclick="checkStatus()">Check Status</button>
            <button onclick="openBrowser()">Open Browser</button>
        </div>
        
        <div class="feature-list">
            <h3>✨ Features</h3>
            <ul>
                <li>🤖 AI Character Chat</li>
                <li>🎭 Role-playing Support</li>
                <li>💬 Real-time Messaging</li>
                <li>🔧 Desktop Integration</li>
                <li>🌐 Web Interface</li>
            </ul>
        </div>
        
        <p><small>Version 0.1.0 - Built with Tauri & Rust</small></p>
    </div>

    <script>
        // Tauri API functions
        const { invoke } = window.__TAURI__.core;
        
        async function startServer() {
            try {
                updateStatus('Starting server...');
                const result = await invoke('start_server');
                updateStatus('Server started: ' + result);
            } catch (error) {
                updateStatus('Error starting server: ' + error);
            }
        }
        
        async function stopServer() {
            try {
                updateStatus('Stopping server...');
                const result = await invoke('stop_server');
                updateStatus('Server stopped: ' + result);
            } catch (error) {
                updateStatus('Error stopping server: ' + error);
            }
        }
        
        async function checkStatus() {
            try {
                updateStatus('Checking status...');
                const result = await invoke('get_server_status');
                updateStatus('Server status: ' + result);
            } catch (error) {
                updateStatus('Error checking status: ' + error);
            }
        }
        
        async function openBrowser() {
            try {
                updateStatus('Opening browser...');
                await invoke('open_browser', { url: 'http://localhost:8080' });
                updateStatus('Browser opened');
            } catch (error) {
                updateStatus('Error opening browser: ' + error);
            }
        }
        
        function updateStatus(message) {
            document.getElementById('server-status').textContent = message;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('Ready');
            checkStatus();
        });
    </script>
</body>
</html>
