{"rustc": 12488743700189009532, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"default-tls\", \"json\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 2383083043656166682, "path": 15257297935374822728, "deps": [[40386456601120721, "percent_encoding", false, 9012493762664266909], [95042085696191081, "ipnet", false, 3424356794827951242], [778154619793643451, "hyper_util", false, 18245025738968059108], [784494742817713399, "tower_service", false, 3682681021102183911], [1288403060204016458, "tokio_util", false, 11835249995689941100], [1811549171721445101, "futures_channel", false, 9073598751111825815], [1906322745568073236, "pin_project_lite", false, 4734008351949582595], [2054153378684941554, "tower_http", false, 1870368805064796704], [2517136641825875337, "sync_wrapper", false, 11622035070159097558], [2883436298747778685, "rustls_pki_types", false, 12426727552491925403], [3150220818285335163, "url", false, 15730960352794477868], [3722963349756955755, "once_cell", false, 13898173386827918274], [4942430025333810336, "webpki_roots", false, 6408477414430469488], [5695049318159433696, "tower", false, 10192046654142321829], [5986029879202738730, "log", false, 15132848699422293037], [7161480121686072451, "rustls", false, 5887590452315723679], [7620660491849607393, "futures_core", false, 15375766359717064194], [9010263965687315507, "http", false, 3756583827281232345], [9538054652646069845, "tokio", false, 1845311123818853277], [9689903380558560274, "serde", false, 18066533925557853048], [10229185211513642314, "mime", false, 1930215642508730926], [10629569228670356391, "futures_util", false, 17733622655136772691], [11895591994124935963, "tokio_rustls", false, 1920906930082466269], [11957360342995674422, "hyper", false, 16162904395828383064], [12186126227181294540, "tokio_native_tls", false, 16563845358978714396], [13077212702700853852, "base64", false, 7372439776013991939], [13330729881774643389, "hyper_rustls", false, 17429020701647597995], [14084095096285906100, "http_body", false, 185161320294960308], [15367738274754116744, "serde_json", false, 11959657437500097803], [16066129441945555748, "bytes", false, 4582155335073800466], [16542808166767769916, "serde_urlencoded", false, 8093079612871783480], [16785601910559813697, "native_tls_crate", false, 6273308036569105457], [16900715236047033623, "http_body_util", false, 6133363444511603795], [18273243456331255970, "hyper_tls", false, 10380948219678478862]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-56385010b187decd\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}