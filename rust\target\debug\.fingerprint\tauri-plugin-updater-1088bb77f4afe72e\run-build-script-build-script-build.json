{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 4611701324750393771], [11721252211900136025, "build_script_build", false, 7487488898907581764]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-updater-1088bb77f4afe72e\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}