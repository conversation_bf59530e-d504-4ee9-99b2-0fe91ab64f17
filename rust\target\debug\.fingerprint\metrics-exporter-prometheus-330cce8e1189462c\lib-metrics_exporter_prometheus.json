{"rustc": 12488743700189009532, "features": "[\"async-runtime\", \"default\", \"http-listener\", \"hyper\", \"hyper-tls\", \"ipnet\", \"push-gateway\", \"tokio\", \"tracing\"]", "declared_features": "[\"async-runtime\", \"default\", \"http-listener\", \"hyper\", \"hyper-tls\", \"ipnet\", \"push-gateway\", \"tokio\", \"tracing\"]", "target": 2221640769694198083, "profile": 5347358027863023418, "path": 14991010690493192650, "deps": [[95042085696191081, "ipnet", false, 3424356794827951242], [3088994813707213335, "quanta", false, 8554910817048705056], [4801984952432540513, "metrics", false, 16580668824251118465], [7414427314941361239, "hyper", false, 7072448872442595550], [8008191657135824715, "thiserror", false, 14582084796005058305], [8606274917505247608, "tracing", false, 1145649429643657630], [9538054652646069845, "tokio", false, 8242399167134721697], [12367227501898450486, "hyper_tls", false, 13114617182703291464], [14483812548788871374, "indexmap", false, 18153470849460234516], [15157431968146737110, "metrics_util", false, 1111325202868220387], [18066890886671768183, "base64", false, 1510129097725295568]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\metrics-exporter-prometheus-330cce8e1189462c\\dep-lib-metrics_exporter_prometheus", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}