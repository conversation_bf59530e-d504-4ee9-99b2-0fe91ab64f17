{"rustc": 12488743700189009532, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 8276155916380437441, "path": 8694853403362143863, "deps": [[947818755262499932, "notify_rust", false, 15683080407366534613], [3150220818285335163, "url", false, 10260461479629395240], [5986029879202738730, "log", false, 7320443587293871305], [9689903380558560274, "serde", false, 7175605069985593048], [10755362358622467486, "tauri", false, 8873184714410150099], [10806645703491011684, "thiserror", false, 14203071536756777833], [12409575957772518135, "time", false, 7015746202650124867], [12783828711503588811, "build_script_build", false, 1848886694113221100], [12986574360607194341, "serde_repr", false, 8271347498386200132], [13208667028893622512, "rand", false, 9557639272206221063], [15367738274754116744, "serde_json", false, 1417905963242708491]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-notification-d4998b8d3d0e5cff\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}