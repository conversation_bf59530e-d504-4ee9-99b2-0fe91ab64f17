{"rustc": 12488743700189009532, "features": "[\"__rustls\", \"__tls\", \"async-compression\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"gzip\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls-crate\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-util\", \"wasm-streams\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 5347358027863023418, "path": 7565996450976943501, "deps": [[40386456601120721, "percent_encoding", false, 9012493762664266909], [95042085696191081, "ipnet", false, 3424356794827951242], [126872836426101300, "async_compression", false, 13013748694968845676], [264090853244900308, "sync_wrapper", false, 7264257863230265960], [784494742817713399, "tower_service", false, 3682681021102183911], [1044435446100926395, "hyper_rustls", false, 15431866714507690422], [1288403060204016458, "tokio_util", false, 11835249995689941100], [1906322745568073236, "pin_project_lite", false, 4734008351949582595], [2779053297469913730, "cookie_crate", false, 777250327357636521], [3150220818285335163, "url", false, 15730960352794477868], [3722963349756955755, "once_cell", false, 13898173386827918274], [4405182208873388884, "http", false, 16560604046070236303], [5986029879202738730, "log", false, 15132848699422293037], [7414427314941361239, "hyper", false, 12453232271446298301], [7620660491849607393, "futures_core", false, 15375766359717064194], [8405603588346937335, "winreg", false, 958340033104904480], [8915503303801890683, "http_body", false, 2131342423004207101], [9538054652646069845, "tokio", false, 1845311123818853277], [9689903380558560274, "serde", false, 18066533925557853048], [10229185211513642314, "mime", false, 1930215642508730926], [10629569228670356391, "futures_util", false, 17733622655136772691], [11295624341523567602, "rustls", false, 5062058830336940301], [12186126227181294540, "tokio_native_tls", false, 16563845358978714396], [12367227501898450486, "hyper_tls", false, 8121013450422423421], [13809605890706463735, "h2", false, 3265233740250342594], [14564311161534545801, "encoding_rs", false, 6984454549018752457], [15367738274754116744, "serde_json", false, 11959657437500097803], [16066129441945555748, "bytes", false, 4582155335073800466], [16311359161338405624, "rustls_pemfile", false, 9595920398270076733], [16542808166767769916, "serde_urlencoded", false, 8093079612871783480], [16622232390123975175, "tokio_rustls", false, 1026279309996401454], [16785601910559813697, "native_tls_crate", false, 6273308036569105457], [17652733826348741533, "webpki_roots", false, 11816704712370582850], [17973378407174338648, "cookie_store", false, 12150536923052691789], [18066890886671768183, "base64", false, 1510129097725295568], [18071510856783138481, "mime_guess", false, 4086097355871731076]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-c590585a920d893d\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}